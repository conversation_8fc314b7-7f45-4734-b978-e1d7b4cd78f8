<?php

/**
 * View Helper Functions
 */

/**
 * Render a view file with optional data
 * 
 * @param string $view Path to the view file (without .php extension)
 * @param array $data Data to be passed to the view
 * @return void
 */
function view($view, $data = [])
{
    // Extract data to make variables available in view
    extract($data);
    
    $viewPath = VIEWS_DIR . '/' . $view . '.php';
    
    if (!file_exists($viewPath)) {
        throw new Exception("View file not found: $viewPath");
    }
    
    // Start output buffering
    ob_start();
    require $viewPath;
    $content = ob_get_clean();
    
    echo $content;
}

/**
 * Generate an asset URL
 * 
 * @param string $path Path to the asset file
 * @return string Full URL to the asset
 */
function asset($path)
{
    return SITE_URL . '/assets/' . ltrim($path, '/');
}

/**
 * Generate a URL to a route
 * 
 * @param string $path Path relative to the site URL
 * @return string Full URL
 */
function url($path = '')
{
    return SITE_URL . '/' . ltrim($path, '/');
}

/**
 * Create a sanitized and limited excerpt from text
 * 
 * @param string $text Original text
 * @param int $length Maximum length of the excerpt
 * @return string Sanitized excerpt
 */
function excerpt($text, $length = 150)
{
    $text = strip_tags($text);
    
    if (strlen($text) <= $length) {
        return $text;
    }
    
    $excerpt = substr($text, 0, $length);
    return $excerpt . '...';
}

/**
 * Generate a shimmer button with Magic UI styling
 *
 * @param string $text Button text
 * @param string $href Button link
 * @param string $variant Button variant ('primary' or 'secondary')
 * @param string $icon Font Awesome icon class (optional)
 * @param array $attributes Additional HTML attributes
 * @param array $aosOptions AOS animation options
 * @return string HTML for shimmer button
 */
function shimmer_button($text, $href = '#', $variant = 'primary', $icon = '', $attributes = [], $aosOptions = [])
{
    // Sanitize inputs
    $text = htmlspecialchars($text);
    $href = htmlspecialchars($href);
    $variant = in_array($variant, ['primary', 'secondary']) ? $variant : 'primary';

    // Build CSS classes
    $baseClasses = 'shimmer-button group font-sans font-bold transition-all duration-300 hover:scale-105 text-base flex items-center justify-center min-w-[260px] focus:outline-none';

    $variantClasses = $variant === 'primary'
        ? 'shimmer-button-primary hover:shadow-xl hover:shadow-primary-500/30 focus:ring-4 focus:ring-primary-400/50'
        : 'shimmer-button-secondary hover:shadow-lg focus:ring-4 focus:ring-white/20';

    $classes = $baseClasses . ' ' . $variantClasses;

    // Build attributes string
    $attributeString = '';
    foreach ($attributes as $key => $value) {
        $attributeString .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
    }

    // Build AOS attributes
    $aosString = '';
    if (!empty($aosOptions)) {
        foreach ($aosOptions as $key => $value) {
            if ($key === 'aos') {
                $aosString .= ' data-aos="' . htmlspecialchars($value) . '"';
            } else {
                $aosString .= ' data-aos-' . $key . '="' . htmlspecialchars($value) . '"';
            }
        }
    }

    // Build icon HTML
    $iconHtml = '';
    if (!empty($icon)) {
        $iconHtml = '<i class="' . htmlspecialchars($icon) . ' ml-2 transition-transform group-hover:translate-x-1"></i>';
    }

    // Generate unique ID for animation timing
    $uniqueId = 'shimmer-' . uniqid();

    return <<<HTML
<a href="{$href}"
   class="{$classes}"
   role="button"
   {$attributeString}
   {$aosString}>

    <!-- Spark container -->
    <div class="shimmer-spark-container">
        <!-- Spark -->
        <div class="shimmer-spark">
            <!-- Spark before -->
            <div class="shimmer-spark-before"></div>
        </div>
    </div>

    <!-- Button content -->
    <span class="relative z-10">{$text}</span>
    {$iconHtml}

    <!-- Highlight -->
    <div class="shimmer-highlight"></div>

    <!-- Backdrop -->
    <div class="shimmer-backdrop"></div>
</a>
HTML;
}
